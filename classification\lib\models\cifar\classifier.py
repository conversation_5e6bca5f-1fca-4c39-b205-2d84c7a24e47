from __future__ import print_function

import torch.nn as nn


#########################################
# ===== Classifiers ===== #
#########################################

class LinearClassifier(nn.Module):

    def __init__(self, dim_in, n_label=10):
        super(LinearClassifier, self).__init__()

        self.net = nn.Linear(dim_in, n_label)

    def forward(self, x):
        return self.net(x)


class NonLinearClassifier(nn.Module):

    def __init__(self, dim_in, n_label=10, p=0.1):
        super(NonLinearClassifier, self).__init__()

        self.net = nn.Sequential(
            nn.Linear(dim_in, 200),
            nn.Dropout(p=p),
            nn.<PERSON>chNorm1d(200),
            nn.<PERSON><PERSON><PERSON>(inplace=True),
            nn.Linear(200, n_label),
        )

    def forward(self, x):
        return self.net(x)
