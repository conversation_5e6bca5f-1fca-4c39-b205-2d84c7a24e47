backbone:
    # name: [stride, in_channels, out_channels, expand_ratio, op]
    conv_stem: [2, 3, 32, 1, 'conv3x3']
    stage_0: [1, 32, 16, 1, 'ir_3x3']
    stage1_1: [2, 16, 32, 3, 'ir_5x5']
    stage1_2: [1, 32 , 32, 3, 'ir_3x3']
    stage1_3: [1, 32, 32, 1, 'id']
    stage1_4: [1, 32, 32, 1, 'id']
    stage2_1: [2, 32, 40, 3, 'ir_7x7']
    stage2_2: [1, 40, 40, 3, 'ir_3x3']
    stage2_3: [1, 40, 40, 3, 'ir_5x5']
    stage2_4: [1, 40, 40, 3, 'ir_5x5']
    stage3_1: [2, 40, 80, 6, 'ir_7x7'] 
    stage3_2: [1, 80, 80, 3, 'ir_5x5'] 
    stage3_3: [1, 80, 80, 3, 'ir_5x5'] 
    stage3_4: [1, 80, 80, 3, 'ir_5x5'] 
    stage3_5: [1, 80, 96, 6, 'ir_5x5'] 
    stage3_6: [1, 96, 96, 3, 'ir_5x5'] 
    stage3_7: [1, 96, 96, 3, 'ir_5x5'] 
    stage3_8: [1, 96, 96, 3, 'ir_5x5'] 
    stage4_1: [2, 96, 192, 6, 'ir_7x7']
    stage4_2: [1, 192, 192, 6, 'ir_7x7'] 
    stage4_3: [1, 192, 192, 3, 'ir_7x7'] 
    stage4_4: [1, 192, 192, 3, 'ir_7x7'] 
    stage5:   [1, 192, 320, 6, 'ir_7x7']
    conv_out: [1, 320, 1280, 1, 'conv1x1']
    gavg_pool: [1, 1280, 1280, 1, 'gavgp']
head:
    linear1:
        dim_in: 1280
        dim_out: 1000

