<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LocalMamba: 图像分类任务中的创新与实现</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .innovation {
            background: linear-gradient(135deg, #ff9a9e, #fecfef);
        }
        .implementation {
            background: linear-gradient(135deg, #a8edea, #fed6e3);
        }
        .results {
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
        }
        .code-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }
        h2 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .code-section h2 {
            color: white;
            border-bottom: 3px solid rgba(255,255,255,0.3);
        }
        .innovation-item {
            background: rgba(255,255,255,0.7);
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border-left: 5px solid #e74c3c;
        }
        .code-block {
            background: rgba(0,0,0,0.1);
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .highlight {
            background: #f39c12;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: bold;
        }
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .performance-table th,
        .performance-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: center;
        }
        .performance-table th {
            background: #3498db;
            color: white;
        }
        .performance-table tr:nth-child(even) {
            background: #f2f2f2;
        }
        .diagram {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: rgba(255,255,255,0.8);
            border-radius: 10px;
        }
        .flow-step {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 25px;
            font-weight: bold;
        }
        .arrow {
            font-size: 1.5em;
            color: #e74c3c;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>LocalMamba</h1>
            <p>Visual State Space Model with Windowed Selective Scan</p>
            <p>图像分类任务中的创新技术分析</p>
        </div>
        
        <div class="content">
            <div class="section innovation">
                <h2>🚀 核心创新点</h2>
                
                <div class="innovation-item">
                    <h3>1. 局部窗口化扫描 (Local Windowed Scan)</h3>
                    <p><strong>问题：</strong>传统Vision Mamba将2D图像展平为1D序列，破坏了局部2D依赖关系，相邻像素间距离变长。</p>
                    <p><strong>创新：</strong>将图像分割为不同窗口，在窗口内进行扫描，既保持局部依赖又维持全局视角。</p>
                    <p><strong>优势：</strong>显著缩短相邻token间的距离，更好地捕获局部特征。</p>
                </div>
                
                <div class="innovation-item">
                    <h3>2. 动态扫描方向搜索 (Dynamic Scan Direction Search)</h3>
                    <p><strong>洞察：</strong>不同网络层对扫描模式有不同偏好。</p>
                    <p><strong>方法：</strong>为每一层独立搜索最优扫描选择，大幅提升性能。</p>
                    <p><strong>实现：</strong>支持8种扫描方向：水平、垂直、窗口化及其翻转版本。</p>
                </div>
                
                <div class="innovation-item">
                    <h3>3. 多尺度窗口策略</h3>
                    <p><strong>特点：</strong>支持不同窗口大小（如w2, w7），适应不同尺度的特征。</p>
                    <p><strong>效果：</strong>在相同计算量下，LocalVim-Ti比Vim-Ti提升3.1%准确率。</p>
                </div>
            </div>
            
            <div class="section implementation">
                <h2>⚙️ 技术实现架构</h2>
                
                <div class="diagram">
                    <h3>LocalMamba处理流程</h3>
                    <div>
                        <span class="flow-step">图像输入</span>
                        <span class="arrow">→</span>
                        <span class="flow-step">Patch嵌入</span>
                        <span class="arrow">→</span>
                        <span class="flow-step">窗口分割</span>
                        <span class="arrow">→</span>
                        <span class="flow-step">多方向扫描</span>
                        <span class="arrow">→</span>
                        <span class="flow-step">选择性状态空间</span>
                        <span class="arrow">→</span>
                        <span class="flow-step">特征融合</span>
                        <span class="arrow">→</span>
                        <span class="flow-step">分类输出</span>
                    </div>
                </div>
                
                <h3>关键组件：</h3>
                <ul>
                    <li><strong>MultiScanVSSM：</strong>多方向扫描的核心模块</li>
                    <li><strong>LocalScan：</strong>窗口化扫描实现</li>
                    <li><strong>BiAttn：</strong>双向注意力机制</li>
                    <li><strong>动态方向选择：</strong>每层独立的扫描方向优化</li>
                </ul>
            </div>
            
            <div class="section results">
                <h2>📊 性能表现</h2>
                
                <table class="performance-table">
                    <thead>
                        <tr>
                            <th>模型</th>
                            <th>数据集</th>
                            <th>分辨率</th>
                            <th>ACC@1</th>
                            <th>参数量</th>
                            <th>FLOPs</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Vim-Ti</td>
                            <td>ImageNet-1K</td>
                            <td>224×224</td>
                            <td>73.1%</td>
                            <td>7M</td>
                            <td>1.5G</td>
                        </tr>
                        <tr style="background: #e8f5e8;">
                            <td><strong>LocalVim-T</strong></td>
                            <td>ImageNet-1K</td>
                            <td>224×224</td>
                            <td><strong>76.2%</strong></td>
                            <td>8M</td>
                            <td>1.5G</td>
                        </tr>
                        <tr>
                            <td>VMamba-S</td>
                            <td>ImageNet-1K</td>
                            <td>224×224</td>
                            <td>83.5%</td>
                            <td>44M</td>
                            <td>11.2G</td>
                        </tr>
                        <tr style="background: #e8f5e8;">
                            <td><strong>LocalVMamba-S</strong></td>
                            <td>ImageNet-1K</td>
                            <td>224×224</td>
                            <td><strong>83.7%</strong></td>
                            <td>50M</td>
                            <td>11.4G</td>
                        </tr>
                    </tbody>
                </table>
                
                <p><span class="highlight">关键提升</span>：在相同FLOPs下，LocalVim-T相比Vim-Ti提升3.1%准确率！</p>
            </div>

            <div class="section code-section">
                <h2>💻 核心代码实现</h2>

                <h3>1. 局部窗口扫描算法</h3>
                <div class="code-block">
<pre>
def local_scan(x, w=7, H=14, W=14, flip=False, column_first=False):
    """LocalMamba的核心窗口化扫描算法
    Args:
        x: 输入张量 [B, L, C]
        w: 窗口大小
        H, W: 原始高度和宽度
        flip: 是否翻转扫描方向
        column_first: 是否列优先扫描
    Returns: [B, C, L] 扫描后的张量
    """
    B, L, C = x.shape
    x = x.view(B, H, W, C)

    # 计算窗口网格数量
    Hg, Wg = math.ceil(H / w), math.ceil(W / w)

    # 填充到窗口大小的整数倍
    if H % w != 0 or W % w != 0:
        newH, newW = Hg * w, Wg * w
        x = F.pad(x, (0, 0, 0, newW - W, 0, newH - H))

    # 重新排列：按窗口内部扫描
    if column_first:
        x = x.view(B, Hg, w, Wg, w, C).permute(0, 5, 3, 1, 4, 2)
    else:
        x = x.view(B, Hg, w, Wg, w, C).permute(0, 5, 1, 3, 2, 4)

    x = x.reshape(B, C, -1)

    if flip:
        x = x.flip([-1])  # 翻转扫描方向

    return x
</pre>
                </div>

                <h3>2. 多方向扫描模块</h3>
                <div class="code-block">
<pre>
class MultiScanVSSM(MultiScan):
    """支持多种扫描方向的视觉状态空间模型"""

    ALL_CHOICES = ('h', 'h_flip', 'v', 'v_flip', 'w2', 'w2_flip', 'w7', 'w7_flip')

    def __init__(self, dim, choices=None):
        super().__init__(dim, choices=choices, token_size=None)
        self.attn = BiAttn(dim)  # 双向注意力

    def multi_scan(self, x):
        """执行多方向扫描
        Args:
            x: [B, C, H, W] 输入特征图
        Returns:
            [B, K, C, H*W] K个方向的扫描结果
        """
        B, C, H, W = x.shape
        self.token_size = (H, W)

        # 对每个选择的方向进行扫描
        xs = super().multi_scan(x)  # [[B, C, H, W], ...]

        # 记录每个方向的序列长度
        self.scan_lengths = [x.shape[2] for x in xs]
        max_length = max(self.scan_lengths)

        # 填充到相同长度以便批量计算
        new_xs = []
        for x in xs:
            if x.shape[2] < max_length:
                x = F.pad(x, (0, max_length - x.shape[2]))
            new_xs.append(x)

        return torch.stack(new_xs, 1)

    def merge(self, xs):
        """融合多方向扫描结果"""
        # 移除填充的token
        xs = [xs[:, i, :, :l] for i, l in enumerate(self.scan_lengths)]
        xs = super().multi_reverse(xs)  # 逆向扫描

        # 应用双向注意力
        xs = [self.attn(x.transpose(-2, -1)) for x in xs]

        # 最终融合
        x = super().forward(xs)
        return x
</pre>
                </div>

                <h3>3. 动态方向搜索</h3>
                <div class="code-block">
<pre>
# 搜索空间定义
ALL_CHOICES = ('h', 'h_flip', 'v', 'v_flip', 'w2', 'w2_flip', 'w7', 'w7_flip')

def search_optimal_directions(model_state):
    """从训练好的搜索空间中提取最优方向"""
    choices = []

    for k, v in model_state.items():
        if 'multi_scan.weights' in k:
            # 计算每个方向的概率
            probs = v.view(-1).softmax(-1)

            # 选择top-4方向
            topk = probs.topk(4)[1].sort()[0].tolist()
            layer_choices = [ALL_CHOICES[idx] for idx in topk]
            choices.append(layer_choices)

    return choices

# 使用示例：为每层设置不同的扫描方向
directions = [
    ('h', 'h_flip', 'w2', 'w2_flip'),    # 第1层：水平+小窗口
    ('v', 'v_flip', 'w7', 'w7_flip'),    # 第2层：垂直+大窗口
    # ... 每层可以有不同的最优组合
]
</pre>
                </div>

                <h3>4. 完整的LocalVim架构</h3>
                <div class="code-block">
<pre>
class VisionMamba(nn.Module):
    """LocalVim主架构"""

    def __init__(self, img_size=224, patch_size=16, depth=20,
                 embed_dim=192, directions=None, **kwargs):
        super().__init__()

        # Patch嵌入
        self.patch_embed = PatchEmbed(
            img_size=img_size, patch_size=patch_size,
            in_chans=3, embed_dim=embed_dim
        )

        # 位置编码
        if if_abs_pos_embed:
            self.pos_embed = nn.Parameter(torch.zeros(1, num_patches, embed_dim))

        # 构建Transformer层
        if directions is None:
            directions = [None] * depth

        self.layers = nn.ModuleList([
            create_block(
                embed_dim,
                directions=directions[i],  # 每层独立的扫描方向
                layer_idx=i,
                **kwargs
            ) for i in range(depth)
        ])

        # 分类头
        self.head = nn.Linear(embed_dim, num_classes)

    def forward(self, x):
        # Patch嵌入
        x = self.patch_embed(x)  # [B, N, C]

        # 添加位置编码
        if hasattr(self, 'pos_embed'):
            x = x + self.pos_embed

        # 通过各层处理
        for layer in self.layers:
            x = layer(x)

        # 全局平均池化 + 分类
        x = x.mean(dim=1)  # [B, C]
        x = self.head(x)   # [B, num_classes]

        return x
</pre>
                </div>
            </div>

            <div class="section">
                <h2>🎯 技术优势总结</h2>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                    <div style="background: #e8f4fd; padding: 20px; border-radius: 10px;">
                        <h3 style="color: #2980b9; margin-top: 0;">算法创新</h3>
                        <ul>
                            <li>窗口化扫描保持局部依赖</li>
                            <li>多方向扫描捕获不同模式</li>
                            <li>动态搜索找到最优配置</li>
                            <li>高效的Triton GPU实现</li>
                        </ul>
                    </div>

                    <div style="background: #f0f9ff; padding: 20px; border-radius: 10px;">
                        <h3 style="color: #16a085; margin-top: 0;">实际效果</h3>
                        <ul>
                            <li>相同FLOPs下准确率提升3.1%</li>
                            <li>支持检测和分割任务</li>
                            <li>模型规模可扩展</li>
                            <li>训练稳定性好</li>
                        </ul>
                    </div>
                </div>

                <div style="background: #fff3cd; padding: 20px; border-radius: 10px; margin-top: 20px; border-left: 5px solid #ffc107;">
                    <h3 style="color: #856404; margin-top: 0;">🔬 核心洞察</h3>
                    <p>LocalMamba的成功在于<strong>重新思考了视觉序列建模的本质</strong>：不是简单地将2D图像展平为1D序列，而是在保持空间局部性的同时引入序列建模的优势。这种"局部化"的思想为Vision Mamba开辟了新的发展方向。</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
