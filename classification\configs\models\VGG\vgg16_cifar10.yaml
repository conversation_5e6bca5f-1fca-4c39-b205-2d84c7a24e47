backbone:
    # name: [n, stride, in_channels, out_channels, expand_ratio, op]
    conv0: [1, 1, 3, 64, 1, 'conv3x3']
    conv1: [1, 1, 64, 64, 1, 'conv3x3']
    pool1: [1, 2, 64, 64, 1, 'maxp']

    conv2: [1, 1, 64, 128, 1, 'conv3x3']
    conv3: [1, 1, 128, 128, 1, 'conv3x3']
    pool2: [1, 2, 128, 128, 1, 'maxp']

    conv4: [1, 1, 128, 256, 1, 'conv3x3']
    conv5: [1, 1, 256, 256, 1, 'conv3x3']
    conv6: [1, 1, 256, 256, 1, 'conv3x3']
    pool3: [1, 2, 256, 256, 1, 'maxp']

    conv7: [1, 1, 256, 512, 1, 'conv3x3']
    conv8: [1, 1, 512, 512, 1, 'conv3x3']
    conv9: [1, 1, 512, 512, 1, 'conv3x3']
    pool4: [1, 2, 512, 512, 1, 'maxp']

    conv10: [1, 1, 512, 512, 1, 'conv3x3']
    conv11: [1, 1, 512, 512, 1, 'conv3x3']
    conv12: [1, 1, 512, 512, 1, 'conv3x3']
    pool5: [1, 1, 512, 512, 1, 'maxp']
    fc: [1, 1, 512, 512, 1, 'linear_relu']
    #pool6:  [1, 1, 512, 512, 1, 'gavgp']

head:
    linear1:
        dim_in: 512
        dim_out: 10
